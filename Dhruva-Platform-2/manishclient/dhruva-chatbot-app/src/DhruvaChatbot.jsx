import { useState, useRef, useEffect } from 'react';
import { Send, Loader2, Bo<PERSON>, User, Settings, X, Globe2, Mic, Upload } from 'lucide-react';

// API configurations for different LLM providers
const LLM_PROVIDERS = {
  OPENAI: {
    name: 'OpenAI',
    endpoint: 'https://api.openai.com/v1/chat/completions',
    headers: (apiKey) => ({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    }),
    preparePayload: (message, conversationHistory) => ({
      model: 'gpt-3.5-turbo',
      messages: conversationHistory || [{ role: 'user', content: message }],
      temperature: 0.7
    }),
    extractResponse: (data) => data.choices[0].message.content
  },
  GEMINI: {
    name: 'Google Gemini',
    endpoint: (apiKey) => `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`,
    headers: () => ({
      'Content-Type': 'application/json'
    }),
    preparePayload: (message, conversationHistory) => ({
      contents: conversationHistory.map(msg => ({
        role: msg.role,
        parts: [{ text: msg.content }]
      })),
      generationConfig: { temperature: 0.7 }
    }),
    extractResponse: (data) => data.candidates[0].content.parts[0].text
  },
  CUSTOM: {
    name: 'Custom LLM',
    endpoint: '', // This will be set from user input
    headers: (apiKey) => ({
      'Content-Type': 'application/json',
      ...(apiKey ? { 'Authorization': `Bearer ${apiKey}` } : {})
    }),
    preparePayload: (message) => ({
      message: message
    }),
    extractResponse: (data) => data.response || data.message || data.content || JSON.stringify(data)
  }
};

const INDIAN_LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'hi', name: 'Hindi' },
  { code: 'ta', name: 'Tamil' },
  { code: 'te', name: 'Telugu' },
  { code: 'kn', name: 'Kannada' },
  { code: 'ml', name: 'Malayalam' },
  { code: 'bn', name: 'Bengali' },
  { code: 'mr', name: 'Marathi' },
  { code: 'gu', name: 'Gujarati' },
  { code: 'pa', name: 'Punjabi' },
  { code: 'or', name: 'Odia' },
];

// Language to script code mapping for dropdown languages
const LANGUAGE_SCRIPT_MAP = {
  en: 'Latn',
  hi: 'Deva',
  ta: 'Taml',
  te: 'Telu',
  kn: 'Knda',
  ml: 'Mlym',
  bn: 'Beng',
  mr: 'Deva',
  gu: 'Gujr',
  pa: 'Guru',
  or: 'Orya',
};

// Translation API utility
async function translateText({ text, sourceLang, targetLang }) {
  if (sourceLang === targetLang) return text;
  const API_KEY = 'Xhf5jWXfkam42bKqEk5PgIusSDsgamh4y0gRL7zs1xUINKQbyI7LX0L02mpMtv09';
  const endpoint = 'http://*************:8000/services/inference/translation';
  const payload = {
    controlConfig: { dataTracking: true },
    config: {
      serviceId: 'ai4bharat/indictrans--gpu-t4',
      language: {
        sourceLanguage: sourceLang,
        sourceScriptCode: LANGUAGE_SCRIPT_MAP[sourceLang] || '',
        targetLanguage: targetLang,
        targetScriptCode: LANGUAGE_SCRIPT_MAP[targetLang] || '',
      },
    },
    input: [{ source: text }],
  };
  const headers = {
    'accept': 'application/json',
    'x-auth-source': 'API_KEY',
    'Authorization': API_KEY,
    'Content-Type': 'application/json',
  };
  try {
    const res = await fetch(endpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify(payload),
    });
    const data = await res.json();
    // Try to extract translated text from response
    if (data && data.output && data.output[0] && data.output[0].target) {
      return data.output[0].target;
    } else if (data && data.output && data.output[0]) {
      // fallback if structure is different
      return data.output[0];
    } else {
      throw new Error('Translation failed');
    }
  } catch (err) {
    console.error('Translation error:', err);
    return text + ' [Translation failed]';
  }
}

// Enhanced ASR API utility with retry mechanism and proper format handling
async function transcribeAudio({ file, sourceLang, retryCount = 0, maxRetries = 3 }) {
  console.log(`[ASR] Starting transcription for language: ${sourceLang} (attempt ${retryCount + 1}/${maxRetries + 1})`);
  console.log(`[ASR] File details:`, {
    name: file.name,
    type: file.type,
    size: file.size
  });

  try {
    const endpoint = '/api/asr';
    const formData = new FormData();

    // Ensure we're sending a WAV file
    let processedFile = file;
    if (file.type !== 'audio/wav') {
      console.log(`[ASR] Converting ${file.type} to WAV format...`);
      try {
        const arrayBuffer = await file.arrayBuffer();
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
        const wavBuffer = audioBufferToWav(audioBuffer);
        const wavBlob = new Blob([wavBuffer], { type: 'audio/wav' });
        processedFile = new File([wavBlob], 'recording.wav', { type: 'audio/wav' });
        console.log(`[ASR] Converted to WAV, new size: ${processedFile.size} bytes`);
      } catch (conversionErr) {
        console.warn('[ASR] Format conversion failed, using original file:', conversionErr);
        // Continue with original file
      }
    }

    formData.append('audio', processedFile);

    // Simplified and consistent config
    const config = {
      controlConfig: { dataTracking: true },
      config: {
        audioFormat: 'wav',
        language: {
          sourceLanguage: sourceLang,
          sourceScriptCode: LANGUAGE_SCRIPT_MAP[sourceLang] || '',
        },
        encoding: 'LINEAR16',
        samplingRate: 16000,
        serviceId: 'ai4bharat/indictasr',
        preProcessors: [],
        postProcessors: [],
        transcriptionFormat: { value: 'transcript' },
        bestTokenCount: 0,
      }
    };

    formData.append('config', JSON.stringify(config));
    formData.append('sampleRate', '16000');
    formData.append('channels', '1');
    formData.append('format', 'wav');

    console.log(`[ASR] Sending request to: ${endpoint}`);
    console.log(`[ASR] Config:`, config);

    const res = await fetch(endpoint, {
      method: 'POST',
      body: formData,
    });

    console.log(`[ASR] Response status: ${res.status} ${res.statusText}`);

    if (!res.ok) {
      let errorText;
      try {
        errorText = await res.text();
      } catch (e) {
        errorText = 'Unable to read error response';
      }
      console.error(`[ASR] Server error: ${res.status} ${res.statusText}`, errorText);

      // Provide specific error messages based on status code
      let errorMessage = `ASR service error: ${res.status}`;
      if (res.status === 500) {
        errorMessage = 'ASR service internal error - audio format may be incompatible';
      } else if (res.status === 400) {
        errorMessage = 'Invalid audio format or parameters';
      } else if (res.status === 413) {
        errorMessage = 'Audio file too large';
      } else if (res.status === 429) {
        errorMessage = 'Too many requests - please wait and try again';
      }

      throw new Error(`${errorMessage} - ${errorText}`);
    }

    const data = await res.json();
    console.log('[ASR] Response data:', data);

    if (data && data.output && data.output[0]) {
      if (data.output[0].transcript) {
        console.log('[ASR] Transcription successful:', data.output[0].transcript);
        return data.output[0].transcript;
      } else if (data.output[0].source) {
        console.log('[ASR] Transcription successful (source):', data.output[0].source);
        return data.output[0].source;
      } else {
        console.error('[ASR] No transcript in response:', data.output[0]);
        throw new Error('No transcript in ASR response');
      }
    } else {
      console.error('[ASR] Invalid response structure:', data);
      throw new Error('Invalid ASR response format');
    }
  } catch (err) {
    console.error(`[ASR] Error on attempt ${retryCount + 1}:`, err);

    // Determine if error is retryable
    const isRetryable = (
      err.message.includes('500') ||
      err.message.includes('502') ||
      err.message.includes('503') ||
      err.message.includes('504') ||
      err.message.includes('network') ||
      err.message.includes('timeout')
    );

    // Retry if possible
    if (isRetryable && retryCount < maxRetries) {
      const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff: 1s, 2s, 4s
      console.log(`[ASR] Retrying in ${delay}ms... (attempt ${retryCount + 2}/${maxRetries + 1})`);

      await new Promise(resolve => setTimeout(resolve, delay));
      return transcribeAudio({ file, sourceLang, retryCount: retryCount + 1, maxRetries });
    }

    throw err;
  }
}

// TTS API utility
async function fetchTTS({ text, lang }) {
  const API_KEY = 'Xhf5jWXfkam42bKqEk5PgIusSDsgamh4y0gRL7zs1xUINKQbyI7LX0L02mpMtv09';
  const endpoint = 'http://*************:8000/services/inference/tts';
  const payload = {
    controlConfig: { dataTracking: true },
    config: {
      serviceId: 'ai4bharat/indictts--gpu-t4',
      gender: 'male',
      samplingRate: 0,
      audioFormat: 'wav',
      language: {
        sourceLanguage: lang,
        sourceScriptCode: LANGUAGE_SCRIPT_MAP[lang] || '',
      }
    },
    input: [
      {
        source: text,
        audioDuration: 0
      }
    ]
  };
  const headers = {
    'accept': 'application/json',
    'x-auth-source': 'API_KEY',
    'Authorization': API_KEY,
    'Content-Type': 'application/json',
  };
  const res = await fetch(endpoint, {
    method: 'POST',
    headers,
    body: JSON.stringify(payload),
  });
  const data = await res.json();
  if (data && data.audio && data.audio[0] && data.audio[0].audioContent) {
    return data.audio[0].audioContent;
  }
  throw new Error('TTS failed');
}

function playBase64Wav(base64) {
  const audio = new Audio('data:audio/wav;base64,' + base64);
  audio.play();
}

// Audio conversion utility - Convert WebM to WAV for ASR compatibility
async function webmBlobToWavBlob(webmBlob) {
  return new Promise((resolve, reject) => {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const fileReader = new FileReader();

    fileReader.onload = async function(e) {
      try {
        const arrayBuffer = e.target.result;
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

        // Convert to WAV format
        const wavBuffer = audioBufferToWav(audioBuffer);
        const wavBlob = new Blob([wavBuffer], { type: 'audio/wav' });

        resolve(wavBlob);
      } catch (error) {
        console.error('Error converting WebM to WAV:', error);
        // Fallback: return original blob if conversion fails
        resolve(webmBlob);
      }
    };

    fileReader.onerror = () => {
      console.error('Error reading WebM file');
      // Fallback: return original blob if reading fails
      resolve(webmBlob);
    };

    fileReader.readAsArrayBuffer(webmBlob);
  });
}

// Convert AudioBuffer to WAV format
function audioBufferToWav(buffer) {
  const length = buffer.length;
  const numberOfChannels = buffer.numberOfChannels;
  const sampleRate = buffer.sampleRate;
  const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
  const view = new DataView(arrayBuffer);

  // WAV header
  const writeString = (offset, string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };

  writeString(0, 'RIFF');
  view.setUint32(4, 36 + length * numberOfChannels * 2, true);
  writeString(8, 'WAVE');
  writeString(12, 'fmt ');
  view.setUint32(16, 16, true);
  view.setUint16(20, 1, true);
  view.setUint16(22, numberOfChannels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, sampleRate * numberOfChannels * 2, true);
  view.setUint16(32, numberOfChannels * 2, true);
  view.setUint16(34, 16, true);
  writeString(36, 'data');
  view.setUint32(40, length * numberOfChannels * 2, true);

  // Convert audio data
  let offset = 44;
  for (let i = 0; i < length; i++) {
    for (let channel = 0; channel < numberOfChannels; channel++) {
      const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
      view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
      offset += 2;
    }
  }

  return arrayBuffer;
}

const SAMPLE_RATES = [8000, 16000, 48000];
const CHANNELS = [1, 2];
const FORMATS = ['wav', 'mp3', 'flac'];

export default function AdvancedChatbot() {
  const [messages, setMessages] = useState([
    { role: 'assistant', content: 'Hello! How can I help you today?' }
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState('OPENAI');
  const [apiKey, setApiKey] = useState('');
  const [customEndpoint, setCustomEndpoint] = useState('');
  const [error, setError] = useState('');
  const [inputLang, setInputLang] = useState('en');
  const [outputLang, setOutputLang] = useState('en');
  const multilingualMode = inputLang !== 'en' || outputLang !== 'en';
  const [showLangMenu, setShowLangMenu] = useState(false);
  const [testText, setTestText] = useState('');
  const [testSourceLang, setTestSourceLang] = useState('hi');
  const [testTargetLang, setTestTargetLang] = useState('en');
  const [testResult, setTestResult] = useState('');
  const [testLoading, setTestLoading] = useState(false);
  const [audioLoading, setAudioLoading] = useState(false);
  const [audioInputLang, setAudioInputLang] = useState('hi');
  const [isRecording, setIsRecording] = useState(false);
  const [micPermission, setMicPermission] = useState(null); // null, 'granted', 'denied', 'prompt'
  const [micError, setMicError] = useState('');
  const [retryCount, setRetryCount] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false); // Prevent concurrent processing
  const mediaRecorderRef = useRef(null);
  const recordedChunksRef = useRef([]);
  const streamRef = useRef(null);
  const processingLockRef = useRef(false); // Processing lock to prevent race conditions
  const recordingSessionIdRef = useRef(0); // Track recording sessions
  
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const langMenuRef = useRef(null);
  const langButtonRef = useRef(null);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Check microphone permissions on component mount
  useEffect(() => {
    checkMicrophonePermission();
  }, []);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      cleanupRecordingResources();
    };
  }, []);

  // Validate recording state before starting new recording
  const validateRecordingState = () => {
    console.log('[Validation] Checking recording state...');

    // Check if already recording
    if (isRecording) {
      console.warn('[Validation] Already recording, cannot start new recording');
      return { valid: false, reason: 'Already recording' };
    }

    // Check if processing
    if (isProcessing || processingLockRef.current) {
      console.warn('[Validation] Still processing previous recording');
      return { valid: false, reason: 'Still processing previous recording' };
    }

    // Check MediaRecorder state
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      console.warn('[Validation] MediaRecorder not in inactive state:', mediaRecorderRef.current.state);
      return { valid: false, reason: `MediaRecorder in ${mediaRecorderRef.current.state} state` };
    }

    // Check if stream is still active
    if (streamRef.current) {
      const activeTracks = streamRef.current.getTracks().filter(track => track.readyState === 'live');
      if (activeTracks.length > 0) {
        console.warn('[Validation] Previous stream still active, cleaning up...');
        cleanupRecordingResources();
      }
    }

    console.log('[Validation] Recording state is valid');
    return { valid: true };
  };

  // Comprehensive cleanup function
  const cleanupRecordingResources = () => {
    console.log('[Cleanup] Starting resource cleanup...');

    try {
      // Stop MediaRecorder if active
      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
        console.log('[Cleanup] Stopping active MediaRecorder');
        mediaRecorderRef.current.stop();
      }

      // Clean up stream
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => {
          if (track.readyState === 'live') {
            track.stop();
            console.log('[Cleanup] Stopped media track');
          }
        });
        streamRef.current = null;
      }

      // Reset refs
      mediaRecorderRef.current = null;
      recordedChunksRef.current = [];
      processingLockRef.current = false;

      // Reset state
      setIsRecording(false);
      setIsProcessing(false);

      console.log('[Cleanup] Resource cleanup completed');

    } catch (err) {
      console.error('[Cleanup] Error during cleanup:', err);
    }
  };

  const checkMicrophonePermission = async () => {
    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        setMicPermission('denied');
        setMicError('Microphone not supported in this browser');
        return;
      }

      // Check permission status if available
      if (navigator.permissions) {
        try {
          const permission = await navigator.permissions.query({ name: 'microphone' });
          setMicPermission(permission.state);

          // Listen for permission changes
          permission.onchange = () => {
            setMicPermission(permission.state);
            if (permission.state === 'denied') {
              setMicError('Microphone permission denied');
              stopRecording();
            }
          };
        } catch (err) {
          console.log('Permission API not supported, will check on first use');
        }
      }
    } catch (err) {
      console.error('Error checking microphone permission:', err);
    }
  };
  
  useEffect(() => {
    if (!showLangMenu) return;
    function handleClickOutside(event) {
      if (
        langMenuRef.current &&
        !langMenuRef.current.contains(event.target) &&
        langButtonRef.current &&
        !langButtonRef.current.contains(event.target)
      ) {
        setShowLangMenu(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showLangMenu]);
  
  const sendMessageToLLM = async (msg) => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await fetch('http://localhost:3001/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: msg.content, provider: 'GEMINI' }),
      });
      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Failed to get response');
      return data.candidates[0].content.parts[0].text;
    } catch (error) {
      console.error('Error:', error);
      setError(error.message);
      return '';
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleSubmit = async () => {
    if (!input.trim() || isLoading) return;
    
    const userMessage = input.trim();
    setInput('');
    
    // Add user message to chat
    setMessages(prev => [...prev, { role: 'user', content: userMessage, inputLang, outputLang }]);
    
    let llmInput = userMessage;
    let llmInputLang = 'en';
    let llmOutputLang = 'en';
    let doTranslateIn = false;
    let doTranslateOut = false;
    if (multilingualMode) {
      if (inputLang !== 'en') {
        llmInput = await translateText({ text: userMessage, sourceLang: inputLang, targetLang: 'en' });
        doTranslateIn = true;
      }
      llmInputLang = 'en';
      llmOutputLang = 'en';
      doTranslateOut = (outputLang !== 'en');
    }
    // If not multilingual, ignore selected input/output languages and use English only
    const response = await sendMessageToLLM({ role: 'user', content: llmInput, inputLang: llmInputLang, outputLang: llmOutputLang });
    let finalResponse = response;
    if (multilingualMode && doTranslateOut) {
      finalResponse = await translateText({ text: response, sourceLang: 'en', targetLang: outputLang });
    }
    
    // Add assistant message to chat
    setMessages(prev => [...prev, { role: 'assistant', content: finalResponse, inputLang, outputLang }]);
    setLlmIO(prev => [...prev, { input: llmInput, output: response }]);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };
  
  const clearChat = () => {
    setMessages([{ role: 'assistant', content: 'Chat cleared. How can I help you today?' }]);
  };
  
  // Temporary translation test handler
  const handleTestTranslation = async () => {
    setTestLoading(true);
    setTestResult('');
    const result = await translateText({ text: testText, sourceLang: testSourceLang, targetLang: testTargetLang });
    setTestResult(result);
    setTestLoading(false);
  };
  
  // Enhanced voice recording handlers with robust error handling
  const startRecording = async (retryAttempt = 0) => {
    try {
      console.log(`[Recording] Starting recording attempt ${retryAttempt + 1}`);

      // Validate state before starting
      const stateValidation = validateRecordingState();
      if (!stateValidation.valid) {
        setMicError(`Cannot start recording: ${stateValidation.reason}`);
        return;
      }

      // Increment session ID for tracking
      recordingSessionIdRef.current++;
      const sessionId = recordingSessionIdRef.current;
      console.log(`[Recording] Session ID: ${sessionId}`);

      setMicError('');
      setIsProcessing(false);

      // Check browser support
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Microphone not supported in this browser. Please use Chrome, Firefox, or Safari.');
      }

      // Request microphone access with constraints
      const constraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000,
          channelCount: 1
        }
      };

      console.log('Requesting microphone access...');
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      streamRef.current = stream;

      // Check if MediaRecorder is supported
      if (!window.MediaRecorder) {
        throw new Error('Audio recording not supported in this browser');
      }

      // Create MediaRecorder with optimal settings for ASR compatibility
      let options = {};

      // Try different MIME types in order of preference for ASR compatibility
      const mimeTypes = [
        'audio/webm;codecs=opus',
        'audio/webm',
        'audio/mp4',
        'audio/ogg;codecs=opus',
        'audio/wav'
      ];

      for (const mimeType of mimeTypes) {
        if (MediaRecorder.isTypeSupported(mimeType)) {
          options.mimeType = mimeType;
          console.log(`[Recording] Using MIME type: ${mimeType}`);
          break;
        }
      }

      // Set audio bit rate for quality
      if (options.mimeType) {
        options.audioBitsPerSecond = 16000;
      }

      mediaRecorderRef.current = new MediaRecorder(stream, options);
      recordedChunksRef.current = [];

      // Log the actual MIME type being used
      console.log(`[Recording] MediaRecorder created with MIME type: ${mediaRecorderRef.current.mimeType}`);



      mediaRecorderRef.current.onstop = async () => {
        console.log(`[Recording] Session ${sessionId} stopped, processing audio...`);
        console.log(`[Recording] Total chunks collected: ${recordedChunksRef.current.length}`);

        // Check if this is still the current session
        if (sessionId !== recordingSessionIdRef.current) {
          console.warn(`[Recording] Session ${sessionId} is outdated, skipping processing`);
          return;
        }

        await processRecordedAudio(sessionId);
      };

      mediaRecorderRef.current.onerror = (e) => {
        console.error(`[Recording] Session ${sessionId} MediaRecorder error:`, e);
        setMicError('Recording error occurred. Please try again.');
        cleanupRecordingResources();
      };

      mediaRecorderRef.current.onstart = () => {
        console.log(`[Recording] Session ${sessionId} MediaRecorder started successfully`);
      };

      mediaRecorderRef.current.ondataavailable = (e) => {
        if (e.data.size > 0) {
          recordedChunksRef.current.push(e.data);
          console.log(`[Recording] Session ${sessionId} chunk: ${e.data.size} bytes, type: ${e.data.type}`);
        }
      };

      // Start recording with smaller time slices for better reliability
      mediaRecorderRef.current.start(500); // Collect data every 500ms
      setIsRecording(true);
      setMicPermission('granted');
      setRetryCount(0);

      console.log('Recording started successfully');

    } catch (err) {
      console.error('Microphone error:', err);

      // Handle specific error types
      let errorMessage = 'Microphone access failed. ';

      if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
        errorMessage = 'Microphone permission denied. Please allow microphone access and try again.';
        setMicPermission('denied');
      } else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') {
        errorMessage = 'No microphone found. Please connect a microphone and try again.';
      } else if (err.name === 'NotReadableError' || err.name === 'TrackStartError') {
        errorMessage = 'Microphone is being used by another application. Please close other apps and try again.';
      } else if (err.name === 'OverconstrainedError' || err.name === 'ConstraintNotSatisfiedError') {
        errorMessage = 'Microphone constraints not supported. Trying with basic settings...';

        // Retry with basic constraints
        if (retryAttempt < 2) {
          console.log('Retrying with basic constraints...');
          setTimeout(() => startRecordingBasic(retryAttempt + 1), 1000);
          return;
        }
      } else {
        errorMessage += err.message || 'Unknown error occurred.';
      }

      setMicError(errorMessage);
      setIsRecording(false);

      // Auto-retry for certain errors
      if (retryAttempt < 1 && (err.name === 'NotReadableError' || err.name === 'AbortError')) {
        console.log(`Auto-retrying in 2 seconds... (attempt ${retryAttempt + 1})`);
        setTimeout(() => startRecording(retryAttempt + 1), 2000);
      }
    }
  };

  // Fallback recording with basic constraints
  const startRecordingBasic = async (retryAttempt = 0) => {
    try {
      console.log('Trying basic microphone constraints...');
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;

      mediaRecorderRef.current = new MediaRecorder(stream);
      recordedChunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (e) => {
        if (e.data.size > 0) recordedChunksRef.current.push(e.data);
      };

      mediaRecorderRef.current.onstop = async () => {
        await processRecordedAudio();
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);
      setMicError('');
      console.log('Basic recording started');

    } catch (err) {
      console.error('Basic recording failed:', err);
      setMicError('Unable to access microphone. Please check your browser settings.');
      setIsRecording(false);
    }
  };
  // Process recorded audio with enhanced error handling and format conversion
  const processRecordedAudio = async (sessionId) => {
    // Prevent concurrent processing
    if (processingLockRef.current) {
      console.warn(`[Processing] Session ${sessionId} - Processing already in progress, skipping`);
      return;
    }

    processingLockRef.current = true;
    setIsProcessing(true);

    try {
      console.log(`[Processing] Session ${sessionId} - Starting audio processing...`);

      // Validate session is still current
      if (sessionId !== recordingSessionIdRef.current) {
        console.warn(`[Processing] Session ${sessionId} is outdated, aborting processing`);
        return;
      }

      if (recordedChunksRef.current.length === 0) {
        throw new Error('No audio data recorded');
      }

      // Validate that we have a valid MediaRecorder reference
      if (!mediaRecorderRef.current) {
        throw new Error('MediaRecorder reference is null');
      }

      console.log('Processing', recordedChunksRef.current.length, 'audio chunks');

      // Get the actual MIME type from MediaRecorder
      const actualMimeType = mediaRecorderRef.current?.mimeType || 'audio/webm';
      console.log(`[Processing] MediaRecorder MIME type: ${actualMimeType}`);

      // Create blob from recorded chunks with correct MIME type
      const audioBlob = new Blob(recordedChunksRef.current, { type: actualMimeType });

      if (audioBlob.size === 0) {
        throw new Error('Recorded audio is empty');
      }

      console.log(`[Processing] Audio blob created - Type: ${audioBlob.type}, Size: ${audioBlob.size} bytes`);

      // Validate audio blob size (should be reasonable for speech)
      if (audioBlob.size < 1000) {
        throw new Error('Recorded audio is too small - please record for at least 1 second');
      }

      if (audioBlob.size > 50 * 1024 * 1024) { // 50MB limit
        throw new Error('Recorded audio is too large - please record shorter clips');
      }

      setAudioLoading(true);
      setMicError('');

      try {
        // Always convert to WAV for maximum ASR compatibility
        console.log(`[Processing] Converting ${audioBlob.type} to WAV format...`);
        console.log(`[Processing] Original blob size: ${audioBlob.size} bytes`);

        let finalFile;

        // Check if already WAV format
        if (audioBlob.type === 'audio/wav') {
          console.log('[Processing] Audio is already in WAV format, using directly');
          finalFile = new File([audioBlob], 'recording.wav', { type: 'audio/wav' });
        } else {
          // Convert to WAV
          try {
            console.log(`[Processing] Converting from ${audioBlob.type} to WAV...`);
            const wavBlob = await webmBlobToWavBlob(audioBlob);
            console.log(`[Processing] WAV conversion successful, size: ${wavBlob.size} bytes`);
            finalFile = new File([wavBlob], 'recording.wav', { type: 'audio/wav' });
          } catch (conversionErr) {
            console.warn('[Processing] WAV conversion failed, using original:', conversionErr);
            // Fallback to original blob if conversion fails
            const fileName = audioBlob.type.includes('webm') ? 'recording.webm' :
                           audioBlob.type.includes('mp4') ? 'recording.mp4' :
                           audioBlob.type.includes('ogg') ? 'recording.ogg' : 'recording.audio';
            finalFile = new File([audioBlob], fileName, { type: audioBlob.type });
          }
        }

        console.log(`[Processing] Sending ${finalFile.type} file to ASR service...`);
        console.log(`[Processing] File details:`, {
          name: finalFile.name,
          type: finalFile.type,
          size: finalFile.size
        });

        const transcript = await transcribeAudio({
          file: finalFile,
          sourceLang: audioInputLang,
        });

        if (transcript && transcript.trim()) {
          setInput(transcript);
          console.log('ASR successful:', transcript);
        } else {
          throw new Error('Empty transcript received');
        }
      } catch (asrErr) {
        console.error('[Processing] ASR failed:', asrErr);

        // Provide specific error messages based on error type
        let errorMsg = '[ASR failed]';
        let userMsg = 'Speech recognition failed. Please try again.';

        if (asrErr.message.includes('500')) {
          errorMsg = '[ASR service error - please try again]';
          userMsg = 'ASR service is experiencing issues. Please try again in a moment.';
        } else if (asrErr.message.includes('network') || asrErr.message.includes('fetch')) {
          errorMsg = '[Network error - check connection]';
          userMsg = 'Network connection issue. Please check your internet and try again.';
        } else if (asrErr.message.includes('format') || asrErr.message.includes('codec')) {
          errorMsg = '[Audio format not supported]';
          userMsg = 'Audio format issue. Please try recording again.';
        } else if (asrErr.message.includes('empty') || asrErr.message.includes('silent')) {
          errorMsg = '[No speech detected - speak louder]';
          userMsg = 'No speech detected. Please speak louder and try again.';
        } else if (asrErr.message.includes('Invalid response format')) {
          errorMsg = '[ASR response format error]';
          userMsg = 'ASR service returned invalid response. Please try again.';
        } else if (asrErr.message.includes('timeout')) {
          errorMsg = '[ASR timeout - try shorter recording]';
          userMsg = 'Request timed out. Please try a shorter recording.';
        }

        setInput(errorMsg);
        setMicError(userMsg);
      }
    } catch (err) {
      console.error(`[Processing] Session ${sessionId} error:`, err);
      setInput('[Recording processing failed]');
      setMicError('Failed to process recording. Please try again.');
    } finally {
      console.log(`[Processing] Session ${sessionId} - Cleanup started`);
      setAudioLoading(false);
      setIsProcessing(false);
      processingLockRef.current = false;

      // Clean up resources after processing
      setTimeout(() => {
        if (sessionId === recordingSessionIdRef.current) {
          cleanupRecordingResources();
        }
      }, 100); // Small delay to ensure UI updates
    }
  };

  const stopRecording = () => {
    try {
      console.log('[Recording] Stop recording requested');

      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
        console.log('[Recording] Stopping active MediaRecorder');
        mediaRecorderRef.current.stop();
      } else {
        console.log('[Recording] MediaRecorder already inactive, cleaning up resources');
        cleanupRecordingResources();
      }

      setIsRecording(false);

    } catch (err) {
      console.error('[Recording] Error stopping recording:', err);
      setIsRecording(false);
      cleanupRecordingResources();
    }
  };
  
  const [ttsLoadingIdx, setTtsLoadingIdx] = useState(null);
  const [audioSampleRate, setAudioSampleRate] = useState(16000);
  const [audioChannels, setAudioChannels] = useState(1);
  const [audioFormat, setAudioFormat] = useState('mp3');
  
  // Feedback state
  const [feedback, setFeedback] = useState({}); // { [msgIdx]: 'up' | 'down' }
  // Track LLM input/output for each message
  const [llmIO, setLlmIO] = useState([]); // [{input: string, output: string}]
  
  // Enhanced audio file upload with validation and format conversion
  const handleAudioUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      // Validate file type
      const allowedTypes = ['audio/wav', 'audio/mp3', 'audio/mpeg', 'audio/ogg', 'audio/webm', 'audio/m4a'];
      if (!file.type.startsWith('audio/') && !allowedTypes.includes(file.type)) {
        setMicError('Please upload a valid audio file (WAV, MP3, OGG, WebM, M4A)');
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        setMicError('Audio file too large. Please upload a file smaller than 10MB.');
        return;
      }

      // Validate file duration (basic check)
      if (file.size < 1000) { // Less than 1KB is likely empty
        setMicError('Audio file appears to be empty or corrupted.');
        return;
      }

      console.log('Processing uploaded audio file:', file.name, file.size, 'bytes');

      setAudioLoading(true);
      setMicError('');

      try {
        // Convert to WAV if needed for better compatibility
        let processedFile = file;
        if (file.type !== 'audio/wav') {
          console.log('Converting uploaded file to WAV format...');
          try {
            const arrayBuffer = await file.arrayBuffer();
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
            const wavBuffer = audioBufferToWav(audioBuffer);
            const wavBlob = new Blob([wavBuffer], { type: 'audio/wav' });
            processedFile = new File([wavBlob], 'converted.wav', { type: 'audio/wav' });
            console.log('File converted to WAV, new size:', processedFile.size, 'bytes');
          } catch (conversionErr) {
            console.log('File conversion failed, using original file:', conversionErr);
            // Use original file if conversion fails
          }
        }

        const transcript = await transcribeAudio({
          file: processedFile,
          sourceLang: audioInputLang,
        });

        if (transcript && transcript.trim()) {
          setInput(transcript);
          console.log('Upload ASR successful:', transcript);
        } else {
          throw new Error('Empty transcript from uploaded file');
        }
      } catch (asrErr) {
        console.error('Upload ASR error:', asrErr);

        let errorMsg = '[Upload ASR failed]';
        if (asrErr.message.includes('format')) {
          errorMsg = '[Unsupported audio format]';
        } else if (asrErr.message.includes('empty')) {
          errorMsg = '[No speech detected in file]';
        } else if (asrErr.message.includes('API error')) {
          errorMsg = '[ASR service unavailable]';
        }

        setInput(errorMsg);
        setMicError('Failed to transcribe uploaded audio. Please try a different file.');
      }
    } catch (err) {
      console.error('Audio upload error:', err);
      setMicError('Failed to process uploaded file. Please try again.');
    } finally {
      setAudioLoading(false);
      // Clear the file input
      event.target.value = '';
    }
  };
  
  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-700 via-blue-600 to-indigo-600 shadow-xl sticky top-0 z-20">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Bot className="h-8 w-8 text-emerald-300 drop-shadow" />
              <h1 className="ml-3 text-2xl font-bold text-white tracking-wide drop-shadow">Dhruva AI Assistant</h1>
            </div>
              <button 
                onClick={() => setShowSettings(!showSettings)}
                className="inline-flex items-center px-4 py-2 border border-white/20 shadow text-base font-semibold rounded-xl text-white bg-gradient-to-r from-emerald-400 to-blue-500 hover:from-emerald-500 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-white transition-colors"
              >
                <Settings className="h-5 w-5 mr-2" />
                Settings
              </button>
          </div>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-white rounded-xl shadow-2xl p-8 w-full max-w-md mx-4">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">Settings</h2>
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  LLM Provider
                </label>
                <select
                  value={selectedProvider}
                  onChange={(e) => setSelectedProvider(e.target.value)}
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white/80 backdrop-blur-sm"
                >
                  <option value="GEMINI">Gemini</option>
                  <option value="OPENAI">OpenAI</option>
                </select>
              </div>
              <div className="flex justify-end space-x-4">
                <button
                  onClick={() => setShowSettings(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setShowSettings(false)}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  Save
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Chat Area */}
      <main className="flex-1 flex flex-col items-center justify-center bg-white">
        <div className="w-full max-w-5xl flex-1 flex flex-col justify-end px-2 sm:px-12 py-6">
          <div className="space-y-4">
            {messages.map((message, index) => (
              <div key={index} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}> 
                <div className={`relative group max-w-[80%] ${message.role === 'user' ? 'ml-auto' : 'mr-auto'}`}> 
                  <div className={`rounded-3xl shadow-lg px-6 py-4 text-lg break-words transition-all duration-200 ${
                    message.role === 'user'
                      ? 'bg-gradient-to-r from-purple-500 to-pink-400 text-white rounded-br-3xl rounded-tr-md'
                      : 'bg-gradient-to-r from-blue-600 to-blue-400 text-white rounded-bl-3xl rounded-tl-md'
                  }`}>
                    <div className="flex items-center mb-1">
                      {message.role === 'assistant' ? (
                        <Bot size={18} className="mr-2 text-emerald-200" />
                      ) : (
                        <User size={18} className="mr-2 text-gray-200" />
                      )}
                      <span className="font-bold text-base">
                        {message.role === 'user' ? 'You' : 'AI Assistant'}
                      </span>
                      {/* Speaker icon for TTS */}
                      {message.role === 'assistant' && (
                        <button
                          className="ml-2 p-1 rounded-full hover:bg-blue-100"
                          title="Speak"
                          onClick={async () => {
                            try {
                              setTtsLoadingIdx(index);
                              const audioBase64 = await fetchTTS({
                                text: message.content,
                                lang: message.inputLang || audioInputLang // fallback to audioInputLang
                              });
                              playBase64Wav(audioBase64);
                            } catch (err) {
                              alert('TTS failed');
                            } finally {
                              setTtsLoadingIdx(null);
                            }
                          }}
                          disabled={ttsLoadingIdx === index}
                        >
                          {ttsLoadingIdx === index ? (
                            <svg className="animate-spin h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                            </svg>
                          ) : (
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-5 w-5 text-blue-500"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                              strokeWidth={2}
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" d="M11 5L6 9H2v6h4l5 4V5z" />
                              <path strokeLinecap="round" strokeLinejoin="round" d="M19 12c0-2.21-1.79-4-4-4m4 4c0 2.21-1.79 4-4 4" />
                            </svg>
                          )}
                        </button>
                      )}
                      {/* Feedback buttons for assistant messages */}
                      {message.role === 'assistant' && (
                        <span className="ml-2 flex items-center space-x-1">
                          <button
                            className={`p-1 rounded-full ${feedback[index] === 'up' ? 'bg-green-100' : 'hover:bg-gray-100'}`}
                            title="Thumbs Up"
                            onClick={() => setFeedback(f => ({ ...f, [index]: f[index] === 'up' ? undefined : 'up' }))}
                          >
                            <span role="img" aria-label="Thumbs Up">👍</span>
                          </button>
                          <button
                            className={`p-1 rounded-full ${feedback[index] === 'down' ? 'bg-red-100' : 'hover:bg-gray-100'}`}
                            title="Thumbs Down"
                            onClick={() => setFeedback(f => ({ ...f, [index]: f[index] === 'down' ? undefined : 'down' }))}
                          >
                            <span role="img" aria-label="Thumbs Down">👎</span>
                          </button>
                        </span>
                      )}
                    </div>
                    <p className="whitespace-pre-wrap text-lg leading-relaxed font-medium drop-shadow-sm">{message.content}</p>
                  </div>
                  {/* LLM input/output display for user and assistant messages */}
                  {llmIO[index] && (
                    <div className="bg-gray-100 text-gray-700 text-xs rounded-b-xl px-4 py-2 mt-0.5">
                      {message.role === 'user' && llmIO[index].input && (
                        <div><span className="font-semibold">LLM Input (English):</span> {llmIO[index].input}</div>
                      )}
                      {message.role === 'assistant' && llmIO[index].output && (
                        <div><span className="font-semibold">LLM Output (English):</span> {llmIO[index].output}</div>
                      )}
                    </div>
                  )}
                  {/* Bubble tail */}
                  <span className={`absolute bottom-0 ${message.role === 'user' ? 'right-0' : 'left-0'} w-4 h-4 bg-inherit rounded-full z-0 translate-y-2 ${message.role === 'user' ? 'translate-x-2' : '-translate-x-2'}`}></span>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
            {isLoading && (
              <div className="flex justify-center items-center py-2">
                <Loader2 className="h-5 w-5 animate-spin text-indigo-600" />
              </div>
            )}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 shadow-sm">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}
            {micError && (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 shadow-sm">
                <div className="flex items-center">
                  <svg className="h-5 w-5 text-orange-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <div>
                    <p className="text-sm text-orange-700 font-medium">Microphone Issue</p>
                    <p className="text-sm text-orange-600">{micError}</p>
                    {micPermission === 'denied' && (
                      <button
                        onClick={checkMicrophonePermission}
                        className="mt-2 text-xs text-orange-600 underline hover:text-orange-800"
                      >
                        Check permissions again
                      </button>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Input Area */}
      <div className="border-t border-blue-100 bg-white backdrop-blur-md shadow-xl sticky bottom-0 z-10">
        <div className="max-w-5xl mx-auto px-2 sm:px-12 py-3">
          <div className="flex items-center space-x-2">
            {/* Language Icon */}
            <div className="relative">
              <button
                ref={langButtonRef}
                onClick={() => setShowLangMenu(v => !v)}
                className="p-2 rounded-full bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-emerald-200"
                title="Select Language"
              >
                <Globe2 className="h-6 w-6 text-blue-500" />
              </button>
              {showLangMenu && (
                <div ref={langMenuRef} className="absolute left-0 bottom-12 w-72 bg-white rounded-xl shadow-2xl p-4 z-50 border border-blue-100 animate-fade-in">
                  <div className="mb-2 text-sm font-semibold text-gray-700">Language Settings</div>
                  <div className="grid grid-cols-1 gap-2">
                    <div>
                      <label className="block text-xs font-medium text-gray-500 mb-1">Text Input Language</label>
                      <select
                        value={inputLang}
                        onChange={e => {
                          setInputLang(e.target.value);
                          if (outputLang === inputLang) setOutputLang(e.target.value);
                        }}
                        className="w-full px-2 py-1 rounded-md border border-gray-300 text-gray-800 bg-white focus:ring-2 focus:ring-blue-400 focus:border-blue-400 appearance-none"
                      >
                        {INDIAN_LANGUAGES.map(lang => (
                          <option key={lang.code} value={lang.code}>{lang.name}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-500 mb-1">Text Output Language</label>
                      <select
                        value={outputLang}
                        onChange={e => setOutputLang(e.target.value)}
                        className="w-full px-2 py-1 rounded-md border border-gray-300 text-gray-800 bg-white focus:ring-2 focus:ring-blue-400 focus:border-blue-400 appearance-none"
                      >
                        {INDIAN_LANGUAGES.map(lang => (
                          <option key={lang.code} value={lang.code}>{lang.name}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-500 mb-1">Audio Input Language</label>
                      <select
                        value={audioInputLang}
                        onChange={e => setAudioInputLang(e.target.value)}
                        className="w-full px-2 py-1 rounded-md border border-gray-300 text-gray-800 bg-white focus:ring-2 focus:ring-blue-400 focus:border-blue-400 appearance-none"
                      >
                        {INDIAN_LANGUAGES.filter(l => l.code !== 'en').map(lang => (
                          <option key={lang.code} value={lang.code}>{lang.name}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="flex items-center gap-2 w-full bg-gray-50 rounded-xl px-3 py-2 shadow-sm">
              {/* Upload Button */}
              <label htmlFor="audio-upload" className="p-2 rounded-full bg-white hover:bg-blue-50 cursor-pointer" title="Upload Audio (WAV/MP3)">
                <Upload className="h-5 w-5 text-blue-500" />
              </label>
              <input
                id="audio-upload"
                type="file"
                accept="audio/*"
                onChange={handleAudioUpload}
                className="hidden"
                disabled={audioLoading}
              />
              {/* Enhanced Mic Button with Status */}
              <div className="relative">
                <button
                  onClick={isRecording ? stopRecording : startRecording}
                  className={`p-2 rounded-full transition-all duration-200 ${
                    isRecording
                      ? 'bg-red-100 hover:bg-red-200 animate-pulse'
                      : micPermission === 'denied'
                        ? 'bg-gray-100 cursor-not-allowed'
                        : 'bg-white hover:bg-blue-50'
                  }`}
                  title={
                    micPermission === 'denied'
                      ? 'Microphone permission denied'
                      : isRecording
                        ? 'Stop Recording'
                        : 'Start Recording'
                  }
                  disabled={audioLoading || isProcessing || micPermission === 'denied'}
                >
                  {audioLoading || isProcessing ? (
                    <svg className="animate-spin h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                    </svg>
                  ) : isRecording ? (
                    <svg className="h-5 w-5 text-red-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                    </svg>
                  ) : micPermission === 'denied' ? (
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1m0 0V7a7 7 0 1114 0v2m-7 4a2 2 0 002-2V7a2 2 0 00-4 0v6a2 2 0 002 2zm-7 4h14M9 16h6" />
                      <path strokeLinecap="round" strokeLinejoin="round" d="M18 8l-8 8M6 8l8 8" />
                    </svg>
                  ) : (
                    <Mic className={`h-5 w-5 ${micPermission === 'granted' ? 'text-green-500' : 'text-blue-500'}`} />
                  )}
                </button>

                {/* Recording indicator */}
                {isRecording && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping"></div>
                )}

                {/* Permission status indicator */}
                {micPermission && (
                  <div className={`absolute -bottom-1 -right-1 w-2 h-2 rounded-full ${
                    micPermission === 'granted' ? 'bg-green-400' :
                    micPermission === 'denied' ? 'bg-red-400' : 'bg-yellow-400'
                  }`}></div>
                )}
              </div>
              {/* Text Input and Send */}
              <input
                type="text"
                ref={inputRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Type your message..."
                className="flex-grow w-full p-3 border border-gray-300 rounded-3xl shadow focus:ring-2 focus:ring-blue-100 focus:border-blue-300 bg-white text-gray-900 placeholder-gray-400 font-medium transition-all duration-200 mx-2"
                disabled={isLoading}
              />
              <button
                onClick={handleSubmit}
                disabled={isLoading || !input.trim()}
                className="inline-flex items-center px-4 py-2 rounded-2xl shadow text-base font-semibold text-blue-500 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-200 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                <Send size={20} />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}